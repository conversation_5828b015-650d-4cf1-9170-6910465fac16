/**
 * Integration tests for UpgradeCommand with condition evaluation
 */

import { beforeEach, describe, expect, it, vi } from "vitest";

import { UpgradeCommand } from "../upgrade.js";

// Mock all dependencies
vi.mock("@openai/agents", () => ({
  Agent: vi.fn().mockImplementation(() => ({})),
  run: vi.fn(),
}));

vi.mock("../../utils/filesystem-manager.js", () => ({
  FileSystemManager: vi.fn().mockImplementation(() => ({
    readFile: vi.fn(),
    writeFile: vi.fn(),
  })),
}));

vi.mock("../../utils/streamlined-file-upgrader.js", () => ({
  StreamlinedFileUpgrader: vi.fn().mockImplementation(() => ({})),
}));

vi.mock("../../utils/dependency-updater.js", () => ({
  DependencyUpdater: vi.fn().mockImplementation(() => ({})),
}));

vi.mock("../../utils/condition-evaluator.js", () => ({
  ConditionEvaluator: vi.fn().mockImplementation(() => ({
    evaluateCondition: vi.fn(),
  })),
}));

vi.mock("fs", () => ({
  existsSync: vi.fn(),
}));

vi.mock("path", () => ({
  join: vi.fn((...args) => args.join("/")),
  dirname: vi.fn(),
  relative: vi.fn(),
}));

vi.mock("url", () => ({
  fileURLToPath: vi.fn(() => "/mock/path"),
}));

describe("UpgradeCommand with Condition Evaluation", () => {
  let upgradeCommand: UpgradeCommand;
  let mockConditionEvaluator: any;
  let mockFsManager: any;
  let mockExistsSync: any;

  beforeEach(async () => {
    const { existsSync } = await import("fs");
    mockExistsSync = existsSync as any;

    upgradeCommand = new UpgradeCommand();

    // Get mock instances and setup mock functions
    mockConditionEvaluator = (upgradeCommand as any).conditionEvaluator;
    mockConditionEvaluator.evaluateCondition = vi.fn();

    mockFsManager = (upgradeCommand as any).fsManager;
    mockFsManager.readFile = vi.fn();
    mockFsManager.writeFile = vi.fn();

    vi.clearAllMocks();
  });

  describe("discoverProjectFiles", () => {
    it("should include files without conditions", async () => {
      mockExistsSync.mockReturnValue(true);
      mockFsManager.readFile.mockResolvedValue("file content");

      // Mock condition evaluation for conditional files
      mockConditionEvaluator.evaluateCondition.mockResolvedValue({
        condition: "Add this file if `src/agent-config.tsx` doesn't import any widgets",
        satisfied: true,
        reasoning: "Test condition satisfied",
        filesAnalyzed: ["src/agent-config.tsx"],
      });

      const discoverProjectFiles = (upgradeCommand as any).discoverProjectFiles.bind(upgradeCommand);
      const files = await discoverProjectFiles("/test/path");

      // Should include files like package.json, main.tsx, etc. that don't have conditions
      expect(files.length).toBeGreaterThan(0);
      expect(files.some((f: any) => f.filePath.includes("package.json"))).toBe(true);
    });

    it("should evaluate conditions for conditional files", async () => {
      mockExistsSync.mockReturnValue(false); // File doesn't exist
      mockFsManager.writeFile.mockResolvedValue(undefined);

      // Mock condition evaluation to return satisfied
      mockConditionEvaluator.evaluateCondition.mockResolvedValue({
        condition: "Add this file if `src/agent-config.tsx` doesn't import any widgets",
        satisfied: true,
        reasoning: "No widget imports found",
        filesAnalyzed: ["src/agent-config.tsx"],
      });

      const discoverProjectFiles = (upgradeCommand as any).discoverProjectFiles.bind(upgradeCommand);
      const files = await discoverProjectFiles("/test/path");

      // Should call condition evaluator for conditional files
      expect(mockConditionEvaluator.evaluateCondition).toHaveBeenCalledWith(
        "Add this file if `src/agent-config.tsx` doesn't import any widgets",
      );

      // Should include the conditional file since condition is satisfied
      expect(files.some((f: any) => f.filePath.includes("widgets/button"))).toBe(true);
    });

    it("should skip files when conditions are not satisfied", async () => {
      mockExistsSync.mockReturnValue(false);

      // Mock condition evaluation to return not satisfied
      mockConditionEvaluator.evaluateCondition.mockResolvedValue({
        condition: "Add this file if `src/agent-config.tsx` doesn't import any widgets",
        satisfied: false,
        reasoning: "Widget imports found",
        filesAnalyzed: ["src/agent-config.tsx"],
      });

      const discoverProjectFiles = (upgradeCommand as any).discoverProjectFiles.bind(upgradeCommand);
      const files = await discoverProjectFiles("/test/path");

      // Should call condition evaluator
      expect(mockConditionEvaluator.evaluateCondition).toHaveBeenCalled();

      // Should not include the conditional file since condition is not satisfied
      expect(files.some((f: any) => f.filePath.includes("widgets/button"))).toBe(false);

      // Should not create the file
      expect(mockFsManager.writeFile).not.toHaveBeenCalledWith(
        expect.stringContaining("widgets/button"),
        expect.anything(),
      );
    });

    it("should handle condition evaluation errors gracefully", async () => {
      mockExistsSync.mockReturnValue(false);

      // Mock condition evaluation to throw error
      mockConditionEvaluator.evaluateCondition.mockRejectedValue(new Error("Evaluation failed"));

      const discoverProjectFiles = (upgradeCommand as any).discoverProjectFiles.bind(upgradeCommand);

      // Should not throw error
      await expect(discoverProjectFiles("/test/path")).resolves.toBeDefined();
    });

    it("should create conditional files when they don't exist and condition is satisfied", async () => {
      mockExistsSync.mockImplementation((path: string) => {
        // Only widgets/button file doesn't exist
        return !path.includes("widgets/button");
      });

      mockFsManager.readFile.mockResolvedValue("existing file content");
      mockFsManager.writeFile.mockResolvedValue(undefined);

      mockConditionEvaluator.evaluateCondition.mockResolvedValue({
        condition: "Add this file if `src/agent-config.tsx` doesn't import any widgets",
        satisfied: true,
        reasoning: "No widget imports found",
        filesAnalyzed: ["src/agent-config.tsx"],
      });

      const discoverProjectFiles = (upgradeCommand as any).discoverProjectFiles.bind(upgradeCommand);
      const files = await discoverProjectFiles("/test/path");

      // Should create the conditional file
      expect(mockFsManager.writeFile).toHaveBeenCalledWith(expect.stringContaining("widgets/button"), "");

      // Should include the file in results
      expect(files.some((f: any) => f.filePath.includes("widgets/button"))).toBe(true);
    });

    it("should not create conditional files when they don't exist and condition is not satisfied", async () => {
      mockExistsSync.mockImplementation((path: string) => {
        // Only widgets/button file doesn't exist
        return !path.includes("widgets/button");
      });

      mockFsManager.readFile.mockResolvedValue("existing file content");

      mockConditionEvaluator.evaluateCondition.mockResolvedValue({
        condition: "Add this file if `src/agent-config.tsx` doesn't import any widgets",
        satisfied: false,
        reasoning: "Widget imports found",
        filesAnalyzed: ["src/agent-config.tsx"],
      });

      const discoverProjectFiles = (upgradeCommand as any).discoverProjectFiles.bind(upgradeCommand);
      const files = await discoverProjectFiles("/test/path");

      // Should not create the conditional file
      expect(mockFsManager.writeFile).not.toHaveBeenCalledWith(
        expect.stringContaining("widgets/button"),
        expect.anything(),
      );

      // Should not include the file in results
      expect(files.some((f: any) => f.filePath.includes("widgets/button"))).toBe(false);
    });
  });
});
