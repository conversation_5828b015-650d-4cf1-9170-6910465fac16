/**
 * Tests for ConditionEvaluator
 */

import { beforeEach, describe, expect, it, vi } from "vitest";

import { ConditionEvaluator } from "../condition-evaluator.js";

// Mock @openai/agents
vi.mock("@openai/agents", () => ({
  Agent: vi.fn().mockImplementation(() => ({})),
  run: vi.fn(),
}));

// Mock FileSystemManager
vi.mock("../filesystem-manager.js", () => ({
  FileSystemManager: vi.fn().mockImplementation(() => ({
    readFile: vi.fn(),
  })),
}));

// Mock fs functions
vi.mock("fs", () => ({
  existsSync: vi.fn(),
  readFileSync: vi.fn(),
}));

describe("ConditionEvaluator", () => {
  let conditionEvaluator: ConditionEvaluator;
  let mockRun: any;
  let mockFsManager: any;
  let mockExistsSync: any;

  beforeEach(async () => {
    // Import the mocked modules
    const { run } = await import("@openai/agents");
    const { existsSync } = await import("fs");

    mockRun = run as any;
    mockExistsSync = existsSync as any;

    conditionEvaluator = new ConditionEvaluator();

    // Get the mock fsManager instance
    mockFsManager = (conditionEvaluator as any).fsManager;

    vi.clearAllMocks();
  });

  describe("evaluateCondition", () => {
    it("should return satisfied=false when no OpenAI API key is available", async () => {
      // Remove API key
      delete process.env.OPENAI_API_KEY;

      const result = await conditionEvaluator.evaluateCondition(
        "Add this file if `src/agent-config.tsx` doesn't import any widgets",
      );

      expect(result.satisfied).toBe(false);
      expect(result.reasoning).toContain("LLM evaluation not available");
    });

    it("should evaluate condition using LLM when API key is available", async () => {
      // Set API key
      process.env.OPENAI_API_KEY = "test-key";

      // Recreate evaluator with API key
      conditionEvaluator = new ConditionEvaluator();
      mockFsManager = (conditionEvaluator as any).fsManager;

      // Mock file existence and content
      mockExistsSync.mockReturnValue(true);
      mockFsManager.readFile.mockResolvedValue(`
        import type { AgentChatConfig } from "@cscs-agent/core";
        
        export const config: AgentChatConfig = {
          agents: []
        };
      `);

      // Mock LLM response
      mockRun.mockResolvedValue({
        finalOutput: JSON.stringify({
          satisfied: true,
          reasoning: "The agent-config.tsx file does not import any widgets",
          filesAnalyzed: ["src/agent-config.tsx"],
        }),
      });

      const result = await conditionEvaluator.evaluateCondition(
        "Add this file if `src/agent-config.tsx` doesn't import any widgets",
      );

      expect(result.satisfied).toBe(true);
      expect(result.reasoning).toContain("does not import any widgets");
    });

    it("should handle LLM evaluation failure gracefully", async () => {
      process.env.OPENAI_API_KEY = "test-key";
      conditionEvaluator = new ConditionEvaluator();
      mockFsManager = (conditionEvaluator as any).fsManager;

      mockExistsSync.mockReturnValue(true);
      mockFsManager.readFile.mockResolvedValue("test content");

      // Mock LLM failure
      mockRun.mockRejectedValue(new Error("LLM API error"));

      const result = await conditionEvaluator.evaluateCondition("Add this file if condition is met");

      expect(result.satisfied).toBe(true); // Should default to satisfied on error
      expect(result.reasoning).toContain("Evaluation failed");
    });

    it("should parse malformed LLM response gracefully", async () => {
      process.env.OPENAI_API_KEY = "test-key";
      conditionEvaluator = new ConditionEvaluator();
      mockFsManager = (conditionEvaluator as any).fsManager;

      mockExistsSync.mockReturnValue(true);
      mockFsManager.readFile.mockResolvedValue("test content");

      // Mock malformed LLM response
      mockRun.mockResolvedValue({
        finalOutput: "This is not valid JSON response",
      });

      const result = await conditionEvaluator.evaluateCondition("Add this file if condition is met");

      expect(result.satisfied).toBe(true); // Should fallback to satisfied
      expect(result.reasoning).toContain("Failed to parse structured response");
    });

    it("should gather relevant files mentioned in condition", async () => {
      process.env.OPENAI_API_KEY = "test-key";
      conditionEvaluator = new ConditionEvaluator();
      mockFsManager = (conditionEvaluator as any).fsManager;

      // Mock specific files exist
      mockExistsSync.mockImplementation((path: string) => {
        return path.includes("agent-config.tsx") || path.includes("main.tsx");
      });

      mockFsManager.readFile.mockImplementation((path: string) => {
        if (path.includes("agent-config.tsx")) {
          return Promise.resolve("agent config content");
        }
        if (path.includes("main.tsx")) {
          return Promise.resolve("main content");
        }
        return Promise.resolve("");
      });

      mockRun.mockResolvedValue({
        finalOutput: JSON.stringify({
          satisfied: false,
          reasoning: "Condition not met",
          filesAnalyzed: ["src/agent-config.tsx"],
        }),
      });

      await conditionEvaluator.evaluateCondition("Add this file if `src/agent-config.tsx` doesn't import any widgets");

      // Verify that relevant files were read
      expect(mockFsManager.readFile).toHaveBeenCalledWith(expect.stringContaining("agent-config.tsx"));
    });
  });

  describe("gatherRelevantFiles", () => {
    it("should extract file paths from condition text", async () => {
      process.env.OPENAI_API_KEY = "test-key";
      conditionEvaluator = new ConditionEvaluator();
      mockFsManager = (conditionEvaluator as any).fsManager;

      mockExistsSync.mockReturnValue(true);
      mockFsManager.readFile.mockResolvedValue("file content");

      const gatherRelevantFiles = (conditionEvaluator as any).gatherRelevantFiles.bind(conditionEvaluator);

      const files = await gatherRelevantFiles(
        "Add this file if `src/agent-config.tsx` and `src/main.tsx` don't import widgets",
        "/test/path",
      );

      expect(Object.keys(files)).toContain("src/agent-config.tsx");
      expect(Object.keys(files)).toContain("src/main.tsx");
    });
  });

  describe("integration scenarios", () => {
    it("should handle widget import detection scenario", async () => {
      process.env.OPENAI_API_KEY = "test-key";
      conditionEvaluator = new ConditionEvaluator();
      mockFsManager = (conditionEvaluator as any).fsManager;

      // Mock agent-config.tsx with no widget imports
      mockExistsSync.mockReturnValue(true);
      mockFsManager.readFile.mockResolvedValue(`
        import type { AgentChatConfig } from "@cscs-agent/core";

        export const config: AgentChatConfig = {
          agents: [{
            name: "Test Agent",
            code: "test",
            description: "Test agent",
            message: {
              blocks: {
                widgets: []
              }
            }
          }]
        };
      `);

      mockRun.mockResolvedValue({
        finalOutput: JSON.stringify({
          satisfied: true,
          reasoning: "The agent-config.tsx file imports AgentChatConfig but no widget components",
          filesAnalyzed: ["src/agent-config.tsx"],
        }),
      });

      const result = await conditionEvaluator.evaluateCondition(
        "Add this file if `src/agent-config.tsx` doesn't import any widgets",
      );

      expect(result.satisfied).toBe(true);
      expect(result.reasoning).toContain("no widget components");
    });

    it("should handle widget import detection when widgets are present", async () => {
      process.env.OPENAI_API_KEY = "test-key";
      conditionEvaluator = new ConditionEvaluator();
      mockFsManager = (conditionEvaluator as any).fsManager;

      // Mock agent-config.tsx with widget imports
      mockExistsSync.mockReturnValue(true);
      mockFsManager.readFile.mockResolvedValue(`
        import type { AgentChatConfig } from "@cscs-agent/core";
        import MyButton from "./widgets/button";
        import Rating from "./widgets/rating";

        export const config: AgentChatConfig = {
          agents: [{
            name: "Test Agent",
            code: "test",
            description: "Test agent",
            message: {
              blocks: {
                widgets: [MyButton, Rating]
              }
            }
          }]
        };
      `);

      mockRun.mockResolvedValue({
        finalOutput: JSON.stringify({
          satisfied: false,
          reasoning: "The agent-config.tsx file already imports widget components: MyButton and Rating",
          filesAnalyzed: ["src/agent-config.tsx"],
        }),
      });

      const result = await conditionEvaluator.evaluateCondition(
        "Add this file if `src/agent-config.tsx` doesn't import any widgets",
      );

      expect(result.satisfied).toBe(false);
      expect(result.reasoning).toContain("already imports widget components");
    });
  });
});
